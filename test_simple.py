#!/usr/bin/env python3
"""Simple test to verify the refactored code works."""

from pathlib import Path
import duckdb

from events_pipeline import check_source_table, SourceTableInfo


def test_check_source_table(tmp_path: Path):
    """Test the check_source_table function with dataclass."""
    # Create a temporary database file using pyte<PERSON>'s tmp_path fixture
    temp_db = tmp_path / "test.duckdb"

    # Create a proper empty DuckDB database
    conn = duckdb.connect(str(temp_db))
    conn.close()

    # Test with empty database (no events table)
    result = check_source_table(str(temp_db))

    # Verify it returns a SourceTableInfo dataclass
    assert isinstance(result, SourceTableInfo)
    assert result.exists is False
    assert result.total_rows == 0
    assert result.min_ts is None
    assert result.max_ts is None

    print("✅ Test passed: check_source_table works with dataclass")
    print(f"   Result: {result}")


def test_pathlib_usage():
    """Test that pathlib.Path works as expected."""
    temp_path = Path("/tmp/test.db")
    assert str(temp_path) == "/tmp/test.db"
    assert temp_path.suffix == ".db"
    print("✅ Test passed: pathlib.Path works correctly")


if __name__ == "__main__":
    print("Running simple tests...")
    test_pathlib_usage()

    # For manual testing, create a temporary directory
    import tempfile
    with tempfile.TemporaryDirectory() as temp_dir:
        temp_path = Path(temp_dir)
        test_check_source_table(temp_path)

    print("All tests passed! 🎉")
