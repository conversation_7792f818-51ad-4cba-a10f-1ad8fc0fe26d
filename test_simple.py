#!/usr/bin/env python3
"""Simple test to verify the refactored code works."""

import tempfile
import os
from pathlib import Path

from events_pipeline import check_source_table, SourceTableInfo


def test_check_source_table():
    """Test the check_source_table function with dataclass."""
    # Create a temporary database file
    with tempfile.NamedTemporaryFile(suffix='.duckdb', delete=False) as f:
        temp_db = f.name
    
    try:
        # Test with empty database (no events table)
        result = check_source_table(temp_db)
        
        # Verify it returns a SourceTableInfo dataclass
        assert isinstance(result, SourceTableInfo)
        assert result.exists is False
        assert result.total_rows == 0
        assert result.min_ts is None
        assert result.max_ts is None
        
        print("✅ Test passed: check_source_table works with dataclass")
        print(f"   Result: {result}")
        
    finally:
        # Clean up
        if os.path.exists(temp_db):
            os.unlink(temp_db)


def test_pathlib_usage():
    """Test that pathlib.Path works as expected."""
    temp_path = Path("/tmp/test.db")
    assert str(temp_path) == "/tmp/test.db"
    assert temp_path.suffix == ".db"
    print("✅ Test passed: pathlib.Path works correctly")


if __name__ == "__main__":
    print("Running simple tests...")
    test_pathlib_usage()
    test_check_source_table()
    print("All tests passed! 🎉")
