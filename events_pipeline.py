#!/usr/bin/env python3
"""Events Pipeline for Duck Lake

This script provides an incremental ETL pipeline specifically for the
ducklake_src.events table. It extracts new events from a source DuckDB database,
stages them in a staging DuckDB database, and promotes them to an existing Duck Lake catalog.

The pipeline tracks state to ensure only new records are processed on each run.
"""

import argparse
import logging
import os
import traceback
from dataclasses import dataclass
from datetime import datetime
from typing import Optional

import duckdb
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)


@dataclass
class SourceTableInfo:
    """Information about the source table."""
    exists: bool
    total_rows: int
    min_ts: Optional[datetime]
    max_ts: Optional[datetime]


@dataclass
class LoadInfo:
    """Information about a data load operation."""
    rows_loaded: int
    started_at: datetime
    finished_at: datetime
    duration: float


# Removed clean_postgres_connection_string as it's not used in DuckDB-only operations


def _connect(database: str | None = None) -> duckdb.DuckDBPyConnection:
    """Return a duckdb connection with required extensions."""
    conn = duckdb.connect(database or ":memory:")
    conn.execute("FORCE INSTALL ducklake FROM core_nightly; LOAD ducklake;")
    return conn


def attach_duck_lake(
    conn: duckdb.DuckDBPyConnection,
    catalog_db_file: str,
    data_path: str,
    alias: str = "lake",
) -> None:
    """Attach an existing Duck Lake catalog using local DuckDB file."""
    stmt = ("ATTACH 'ducklake:{catalog_db}' AS {alias} (DATA_PATH '{data}');").format(
        catalog_db=catalog_db_file.replace("'", "''"), alias=alias, data=data_path
    )
    conn.execute(stmt)


def get_ducklake_schema_path(
    conn: duckdb.DuckDBPyConnection, alias: str = "lake"
) -> str:
    """Get the correct schema path for Duck Lake tables.

    Duck Lake might create tables in a specific schema pattern.
    This function helps identify the correct path.
    """
    try:
        # First, let's see all catalogs and schemas
        all_schemas = conn.execute("""
            SELECT DISTINCT catalog_name, schema_name 
            FROM information_schema.schemata 
            ORDER BY catalog_name, schema_name
        """).fetchall()

        # Look for the actual Duck Lake catalog (not the metadata catalog)
        # The alias should match a catalog that's not __ducklake_metadata_*
        duck_lake_schemas = conn.execute(f"""
            SELECT DISTINCT catalog_name, schema_name 
            FROM information_schema.schemata 
            WHERE catalog_name = '{alias}'
               AND schema_name NOT IN ('information_schema', 'pg_catalog')
            ORDER BY catalog_name, schema_name
        """).fetchall()

        # Use the main schema in the Duck Lake catalog
        for cat, schema in duck_lake_schemas:
            if schema == "main":
                return f"{cat}.{schema}"

        # If no main schema, use the first available schema
        for cat, schema in duck_lake_schemas:
            return f"{cat}.{schema}"

        # If we still haven't found anything, just use lake.main as default
        return f"{alias}.main"

    except Exception as e:
        logger.error(f"Error in get_ducklake_schema_path: {e}")
        logger.error(traceback.format_exc())
        return f"{alias}.main"


def check_source_table(src_db_file: str) -> SourceTableInfo:
    """Check if source table exists and get basic info."""
    with duckdb.connect(src_db_file) as src_conn:
        # Check if source table exists
        result = src_conn.execute("""
            SELECT COUNT(*)
            FROM information_schema.tables
            WHERE table_schema = 'ducklake_src'
            AND table_name = 'events'
        """).fetchone()

        if not result or result[0] == 0:
            return SourceTableInfo(exists=False, total_rows=0, min_ts=None, max_ts=None)

        # Get total rows
        total_result = src_conn.execute(
            "SELECT COUNT(*) FROM ducklake_src.events"
        ).fetchone()
        total_rows = total_result[0] if total_result else 0

        # Get min/max timestamps
        min_max_result = src_conn.execute("""
            SELECT MIN(event_timestamp), MAX(event_timestamp)
            FROM ducklake_src.events
        """).fetchone()
        min_ts, max_ts = min_max_result if min_max_result else (None, None)

        return SourceTableInfo(
            exists=True,
            total_rows=total_rows,
            min_ts=min_ts,
            max_ts=max_ts
        )


def setup_staging_table(staging_conn: duckdb.DuckDBPyConnection, dataset_name: str) -> None:
    """Create staging schema and table if they don't exist."""
    staging_conn.execute(f"CREATE SCHEMA IF NOT EXISTS {dataset_name}")
    staging_conn.execute(f"""
        CREATE TABLE IF NOT EXISTS {dataset_name}.events (
            event_id VARCHAR,
            event_type VARCHAR,
            event_timestamp TIMESTAMP,
            details JSON,
            created_at TIMESTAMP
        )
    """)


def get_last_processed_timestamp(staging_conn: duckdb.DuckDBPyConnection, dataset_name: str) -> Optional[datetime]:
    """Get the last processed timestamp from staging."""
    result = staging_conn.execute(f"""
        SELECT MAX(event_timestamp) FROM {dataset_name}.events
    """).fetchone()
    return result[0] if result and result[0] else None


def copy_new_events(staging_conn: duckdb.DuckDBPyConnection, dataset_name: str, last_timestamp: Optional[datetime]) -> int:
    """Copy new events from source to staging and return count."""
    # Build incremental query
    if last_timestamp:
        where_clause = f"WHERE event_timestamp > TIMESTAMP '{last_timestamp}'"
    else:
        where_clause = ""

    # Copy new events from source to staging
    insert_query = f"""
        INSERT INTO {dataset_name}.events
        SELECT
            event_id,
            event_type,
            event_timestamp,
            details,
            created_at
        FROM source_db.ducklake_src.events
        {where_clause}
        ORDER BY event_timestamp
    """

    staging_conn.execute(insert_query)

    # Get count of inserted rows
    if last_timestamp:
        new_rows = staging_conn.execute(f"""
            SELECT COUNT(*) FROM {dataset_name}.events
            WHERE event_timestamp > TIMESTAMP '{last_timestamp}'
        """).fetchone()[0]
    else:
        new_rows = staging_conn.execute(f"""
            SELECT COUNT(*) FROM {dataset_name}.events
        """).fetchone()[0]

    return new_rows


def extract_events_incremental(
    src_db_file: str,
    staging_db_file: str = "events_staging.duckdb",
    dataset_name: str = "staging",
) -> LoadInfo:
    """Extract events table incrementally from source DuckDB to staging DuckDB.

    Uses the event_timestamp to load only new records since the last run.
    Tracks state to ensure only new records are processed.
    """
    # Check source table
    source_info = check_source_table(src_db_file)
    if not source_info.exists:
        now = datetime.now()
        return LoadInfo(
            rows_loaded=0,
            started_at=now,
            finished_at=now,
            duration=0.0,
        )

    # Connect to staging database
    try:
        with duckdb.connect(staging_db_file) as staging_conn:
            # Attach source database to staging database FIRST
            staging_conn.execute(f"ATTACH '{src_db_file}' AS source_db")

            # Setup staging table
            setup_staging_table(staging_conn, dataset_name)

            # Get the last processed timestamp from staging
            last_timestamp = get_last_processed_timestamp(staging_conn, dataset_name)

            # Copy new events from source to staging
            start_time = datetime.now()
            new_rows = copy_new_events(staging_conn, dataset_name, last_timestamp)
            end_time = datetime.now()



            return LoadInfo(
                rows_loaded=new_rows,
                started_at=start_time,
                finished_at=end_time,
                duration=(end_time - start_time).total_seconds(),
            )

    except Exception as e:
        logger.error(f"Error during extraction: {e}")
        logger.error(traceback.format_exc())
        now = datetime.now()
        return LoadInfo(
            rows_loaded=0,
            started_at=now,
            finished_at=now,
            duration=0.0,
        )


def promote_events_to_lake(
    staging_db_file: str,
    catalog_conn: str,
    data_path: str,
    dataset_name: str = "staging",
    alias: str = "lake",
) -> int:
    """Promote staged events to Duck Lake using INSERT ... SELECT.

    This function assumes the events table already exists in Duck Lake
    and uses INSERT to add only the new records.
    """
    conn = _connect(staging_db_file)

    # Get the catalog name (usually the database filename without extension)
    catalog_name = os.path.splitext(os.path.basename(staging_db_file))[0]

    # Check what schemas exist in staging DB BEFORE attaching Duck Lake
    schemas = conn.execute(
        "SELECT schema_name FROM information_schema.schemata"
    ).fetchall()

    # Check if the staging schema exists
    if dataset_name not in [s[0] for s in schemas]:
        return 0

    # Check if events table exists in staging
    tables = conn.execute(f"""
        SELECT table_name
        FROM information_schema.tables
        WHERE table_schema = '{dataset_name}'
    """).fetchall()

    if "events" not in [t[0] for t in tables]:
        return 0

    # Count new records in staging using full path
    staging_count = conn.execute(
        f"SELECT COUNT(*) FROM {catalog_name}.{dataset_name}.events"
    ).fetchone()[0]

    if staging_count == 0:
        return 0

    # Commit any pending staging operations before attaching Duck Lake
    try:
        conn.commit()
    except Exception as e:
        logger.warning(f"Could not commit staging operations: {e}")

    # Attach Duck Lake catalog

    try:
        attach_duck_lake(conn, catalog_conn, data_path, alias)
    except Exception as e:
        logger.error(f"Error attaching Duck Lake: {e}")
        logger.error(traceback.format_exc())
        raise

    # Get the correct schema path for Duck Lake
    ducklake_path = get_ducklake_schema_path(conn, alias)

    # Start a fresh transaction for Duck Lake operations
    try:
        conn.execute("COMMIT")  # Ensure no pending transaction
    except Exception:
        pass  # Ignore if no transaction active

    # Check if events table exists in Duck Lake
    try:
        conn.execute("BEGIN")
        table_exists = (
            conn.execute(f"""
            SELECT COUNT(*) 
            FROM information_schema.tables 
            WHERE table_name = 'events' 
            AND table_catalog || '.' || table_schema = '{ducklake_path}'
        """).fetchone()[0]
            > 0
        )
        conn.execute("COMMIT")
    except Exception as e:
        logger.error(f"Error checking if events table exists: {e}")
        logger.error(traceback.format_exc())
        try:
            conn.execute("ROLLBACK")
        except Exception:
            pass
        # Try a simpler approach
        try:
            conn.execute("BEGIN")
            conn.execute(f"SELECT 1 FROM {ducklake_path}.events LIMIT 1")
            table_exists = True
            conn.execute("COMMIT")
        except Exception:
            table_exists = False
            try:
                conn.execute("ROLLBACK")
            except Exception:
                pass

    if not table_exists:
        # Create the table if it doesn't exist
        try:
            conn.execute("BEGIN")
            conn.execute(f"""
                CREATE TABLE IF NOT EXISTS {ducklake_path}.events (
                    event_id VARCHAR,
                    event_type VARCHAR,
                    event_timestamp TIMESTAMP,
                    details JSON,
                    created_at TIMESTAMP
                )
            """)
            conn.execute("COMMIT")

            # Now insert the data in a new transaction
            conn.execute("BEGIN")
            conn.execute(f"""
                INSERT INTO {ducklake_path}.events
                SELECT
                    event_id::VARCHAR,
                    event_type::VARCHAR,
                    event_timestamp::TIMESTAMP,
                    details::JSON,
                    created_at::TIMESTAMP
                FROM {catalog_name}.{dataset_name}.events
            """)
            conn.execute("COMMIT")
        except Exception as e:
            logger.error(f"Failed to create table with explicit structure: {e}")
            logger.error(traceback.format_exc())
            try:
                conn.execute("ROLLBACK")
            except Exception:
                pass
            raise
    else:
        # Insert new records, avoiding duplicates based on event_id
        try:
            conn.execute("BEGIN")
            conn.execute(f"""
                INSERT INTO {ducklake_path}.events
                SELECT
                    s.event_id::VARCHAR,
                    s.event_type::VARCHAR,
                    s.event_timestamp::TIMESTAMP,
                    s.details::JSON,
                    s.created_at::TIMESTAMP
                FROM {catalog_name}.{dataset_name}.events s
                WHERE NOT EXISTS (
                    SELECT 1 FROM {ducklake_path}.events l
                    WHERE l.event_id = s.event_id
                )
            """)
            conn.execute("COMMIT")
        except Exception as e:
            logger.error(f"Error inserting events: {e}")
            logger.error(traceback.format_exc())
            try:
                conn.execute("ROLLBACK")
            except Exception:
                pass
            raise

    return staging_count


def run_events_pipeline(
    src_db_file: str,
    catalog_db_file: str,
    data_path: str,
    staging_db: str = "events_staging.duckdb",
) -> None:
    """Run the complete events pipeline: extract → stage → promote."""

    print("🚀 Starting Events Pipeline")
    print("=" * 60)

    # Step 1: Extract events incrementally
    print("\n📥 Step 1: Extracting new events from source DuckDB...")
    load_info = extract_events_incremental(
        src_db_file=src_db_file,
        staging_db_file=staging_db,
        dataset_name="staging",  # Explicitly set to avoid confusion
    )

    print("✅ Extraction complete:")
    print(f"   - Rows loaded: {load_info.rows_loaded}")
    print(f"   - Duration: {load_info.duration:.2f} seconds")

    # Step 2: Promote to Duck Lake
    print("\n📤 Step 2: Promoting events to Duck Lake...")
    promoted_count = promote_events_to_lake(
        staging_db_file=staging_db,
        catalog_conn=catalog_db_file,
        data_path=data_path,
        dataset_name="staging",  # Match the extraction dataset name
    )

    print(f"✅ Promotion complete: {promoted_count} events promoted")

    # Step 3: Verify results
    print("\n📊 Step 3: Verifying results...")
    conn = _connect()
    attach_duck_lake(conn, catalog_db_file, data_path)

    # Get the correct Duck Lake path
    ducklake_path = get_ducklake_schema_path(conn)

    # Get total count and recent events
    total_count = conn.execute(
        f"SELECT COUNT(*) FROM {ducklake_path}.events"
    ).fetchone()[0]
    recent_events = conn.execute(f"""
        SELECT event_type, COUNT(*) as count
        FROM {ducklake_path}.events
        WHERE event_timestamp >= CURRENT_TIMESTAMP - INTERVAL '1 hour'
        GROUP BY event_type
        ORDER BY count DESC
    """).fetchall()

    print(f"\n✅ Total events in Duck Lake: {total_count}")
    if recent_events:
        print("\n📈 Recent events (last hour):")
        for event_type, count in recent_events:
            print(f"   - {event_type}: {count}")

    print("\n✨ Pipeline completed successfully!")


def main() -> int:
    """CLI entry point."""
    parser = argparse.ArgumentParser(
        description="Incremental ETL pipeline for ducklake_src.events table"
    )

    # Database file arguments
    parser.add_argument(
        "--source",
        default="events.duckdb",
        help="Source DuckDB database file path (default: events.duckdb)",
    )
    parser.add_argument(
        "--catalog",
        default="catalog.duckdb",
        help="Duck Lake catalog DuckDB database file path (default: catalog.duckdb)",
    )
    parser.add_argument(
        "--data-path",
        default="./ducklake-data/",
        help="Local directory path for Duck Lake data storage (default: ./ducklake-data/)",
    )

    # Staging options
    parser.add_argument(
        "--staging-db",
        default="events_staging.duckdb",
        help="Staging database file (default: events_staging.duckdb)",
    )

    # Actions
    parser.add_argument("--run", action="store_true", help="Run the complete pipeline")
    parser.add_argument(
        "--extract-only", action="store_true", help="Only run the extraction step"
    )
    parser.add_argument(
        "--promote-only",
        action="store_true",
        help="Only run the promotion step (assumes extraction already done)",
    )
    parser.add_argument(
        "--reset-state",
        action="store_true",
        help="Reset the incremental loading state (will reload all data)",
    )

    args = parser.parse_args()

    # Validate required arguments
    if not args.source:
        print("Error: No source database file provided. Use --source")
        return 1

    if not args.catalog:
        print("Error: No catalog database file provided. Use --catalog")
        return 1

    # Reset state if requested
    if args.reset_state:
        print("Resetting incremental loading state...")
        import shutil

        state_path = os.path.expanduser("~/.dlt/pipelines/events_incremental")
        if os.path.exists(state_path):
            shutil.rmtree(state_path)
            print("✅ State reset. Next run will load all data.")
        else:
            print("No state found to reset.")

        # Also delete staging database if it exists
        if os.path.exists(args.staging_db):
            os.remove(args.staging_db)
            print(f"✅ Removed staging database: {args.staging_db}")

    try:
        if args.extract_only:
            print("Running extraction only...")
            load_info = extract_events_incremental(
                src_db_file=args.source, staging_db_file=args.staging_db
            )
            print(f"✅ Extracted {load_info.rows_loaded} rows")

        elif args.promote_only:
            print("Running promotion only...")
            promoted = promote_events_to_lake(
                staging_db_file=args.staging_db,
                catalog_conn=args.catalog,
                data_path=args.data_path,
            )
            print(f"✅ Promoted {promoted} events")

        else:  # Default: run complete pipeline
            run_events_pipeline(
                src_db_file=args.source,
                catalog_db_file=args.catalog,
                data_path=args.data_path,
                staging_db=args.staging_db,
            )

    except Exception as e:
        print(f"❌ Error: {e}")
        traceback.print_exc()
        return 1

    return 0


if __name__ == "__main__":
    exit(main())
