"""Integration tests for the DuckDB-to-DuckDB events pipeline."""

from datetime import datetime, timedelta
from pathlib import Path

import duckdb
import pytest

from events_pipeline import extract_events_incremental, promote_events_to_lake


def create_test_source_data(db_file: str, num_events: int = 10) -> None:
    """Create a test source database with sample events."""
    conn = duckdb.connect(db_file)
    
    # Create the schema and table
    conn.execute("CREATE SCHEMA IF NOT EXISTS ducklake_src")
    conn.execute("""
        CREATE TABLE IF NOT EXISTS ducklake_src.events (
            event_id VARCHAR,
            event_type VARCHAR,
            event_timestamp TIMESTAMP,
            details JSON,
            created_at TIMESTAMP
        )
    """)
    
    # Insert some test data
    base_time = datetime.now() - timedelta(days=1)
    test_events = []
    
    for i in range(num_events):
        event_time = base_time + timedelta(hours=i)
        test_events.append((
            f"event_{i:03d}",
            "test_event",
            event_time,
            f'{{"user_id": {i}, "action": "test_action_{i}"}}',
            event_time
        ))
    
    conn.executemany("""
        INSERT INTO ducklake_src.events 
        (event_id, event_type, event_timestamp, details, created_at)
        VALUES (?, ?, ?, ?, ?)
    """, test_events)
    
    conn.close()


def create_test_catalog(catalog_file: str, data_path: str) -> None:
    """Create a test Duck Lake catalog."""
    # Ensure data directory exists
    Path(data_path).mkdir(parents=True, exist_ok=True)
    
    # Create a simple catalog database
    conn = duckdb.connect(catalog_file)
    conn.execute("FORCE INSTALL ducklake FROM core_nightly; LOAD ducklake;")
    
    # Initialize Duck Lake catalog
    conn.execute("CREATE SCHEMA IF NOT EXISTS main")
    
    conn.close()


class TestPipelineIntegration:
    """Test the complete pipeline integration."""
    
    def test_complete_pipeline(
        self, 
        tmp_path: Path,
        temp_catalog_db: str,
        temp_data_path: str
    ) -> None:
        """Test the complete DuckDB-to-DuckDB pipeline."""
        # File paths
        source_db = tmp_path / "source.duckdb"
        staging_db = tmp_path / "staging.duckdb"
        
        # Step 1: Create test data
        create_test_source_data(str(source_db), num_events=10)
        
        # Step 2: Create test catalog
        create_test_catalog(temp_catalog_db, temp_data_path)
        
        # Step 3: Test extraction
        load_info = extract_events_incremental(
            src_db_file=str(source_db),
            staging_db_file=str(staging_db),
            dataset_name="events_staging"
        )
        
        assert load_info.rows_loaded == 10
        
        # Step 4: Test promotion
        promoted_count = promote_events_to_lake(
            staging_db_file=str(staging_db),
            catalog_conn=temp_catalog_db,
            data_path=temp_data_path,
            dataset_name="events_staging"
        )
        
        assert promoted_count == 10
        
        # Step 5: Test incremental extraction (should find no new events)
        load_info2 = extract_events_incremental(
            src_db_file=str(source_db),
            staging_db_file=str(staging_db),
            dataset_name="events_staging"
        )
        
        assert load_info2.rows_loaded == 0
        
        # Step 6: Add more data and test incremental
        conn = duckdb.connect(str(source_db))
        new_time = datetime.now()
        conn.execute("""
            INSERT INTO ducklake_src.events 
            (event_id, event_type, event_timestamp, details, created_at)
            VALUES (?, ?, ?, ?, ?)
        """, ("event_new", "new_event", new_time, '{"new": true}', new_time))
        conn.close()
        
        load_info3 = extract_events_incremental(
            src_db_file=str(source_db),
            staging_db_file=str(staging_db),
            dataset_name="events_staging"
        )
        
        assert load_info3.rows_loaded == 1
    
    def test_empty_source_handling(
        self,
        empty_source_db: str,
        temp_staging_db: str,
        temp_catalog_db: str,
        temp_data_path: str
    ) -> None:
        """Test handling of empty source database."""
        # Test extraction from empty source
        load_info = extract_events_incremental(
            src_db_file=empty_source_db,
            staging_db_file=temp_staging_db,
            dataset_name="events_staging"
        )
        
        assert load_info.rows_loaded == 0
        
        # Test promotion with no data
        promoted_count = promote_events_to_lake(
            staging_db_file=temp_staging_db,
            catalog_conn=temp_catalog_db,
            data_path=temp_data_path,
            dataset_name="events_staging"
        )
        
        assert promoted_count == 0
