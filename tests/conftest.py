"""Shared pytest fixtures for ducklake-speedrun tests."""

from datetime import datetime, timedelta
from pathlib import Path
from typing import Generator

import duckdb
import pytest


@pytest.fixture
def temp_source_db(tmp_path: Path) -> Generator[str, None, None]:
    """Create a temporary source database with test data."""
    db_file = tmp_path / "source.duckdb"
    
    conn = duckdb.connect(str(db_file))
    
    # Create the schema and table
    conn.execute("CREATE SCHEMA IF NOT EXISTS ducklake_src")
    conn.execute("""
        CREATE TABLE IF NOT EXISTS ducklake_src.events (
            event_id VARCHAR,
            event_type VARCHAR,
            event_timestamp TIMESTAMP,
            details JSON,
            created_at TIMESTAMP
        )
    """)
    
    # Insert some test data
    base_time = datetime.now() - timedelta(days=1)
    test_events = []
    
    for i in range(5):
        event_time = base_time + timedelta(hours=i)
        test_events.append((
            f"event_{i:03d}",
            "test_event",
            event_time,
            f'{{"user_id": {i}, "action": "test_action_{i}"}}',
            event_time
        ))
    
    conn.executemany("""
        INSERT INTO ducklake_src.events 
        (event_id, event_type, event_timestamp, details, created_at)
        VALUES (?, ?, ?, ?, ?)
    """, test_events)
    
    conn.close()
    
    yield str(db_file)


@pytest.fixture
def temp_staging_db(tmp_path: Path) -> Generator[str, None, None]:
    """Create a temporary staging database."""
    db_file = tmp_path / "staging.duckdb"
    yield str(db_file)


@pytest.fixture
def empty_source_db(tmp_path: Path) -> Generator[str, None, None]:
    """Create a temporary source database without the events table."""
    db_file = tmp_path / "empty_source.duckdb"
    
    # Just create an empty database
    conn = duckdb.connect(str(db_file))
    conn.close()
    
    yield str(db_file)


@pytest.fixture
def temp_catalog_db(tmp_path: Path) -> Generator[str, None, None]:
    """Create a temporary Duck Lake catalog database."""
    db_file = tmp_path / "catalog.duckdb"
    yield str(db_file)


@pytest.fixture
def temp_data_path(tmp_path: Path) -> Generator[str, None, None]:
    """Create a temporary data directory for Duck Lake."""
    data_dir = tmp_path / "ducklake-data"
    data_dir.mkdir(exist_ok=True)
    yield str(data_dir)
