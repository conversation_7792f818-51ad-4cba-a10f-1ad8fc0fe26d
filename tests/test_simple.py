"""Simple test to verify the refactored code works."""

from pathlib import Path
import duckdb

from events_pipeline import check_source_table, SourceTableInfo


def test_check_source_table(tmp_path: Path):
    """Test the check_source_table function with dataclass."""
    # Arrange
    temp_db = tmp_path / "test.duckdb"
    conn = duckdb.connect(str(temp_db))
    conn.close()

    # Act
    result = check_source_table(temp_db)

    # Assert
    assert isinstance(result, SourceTableInfo)
    assert result.exists is False
    assert result.total_rows == 0
    assert result.min_ts is None
    assert result.max_ts is None
